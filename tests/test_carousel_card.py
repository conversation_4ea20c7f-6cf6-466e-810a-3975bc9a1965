from app.models.carousel_card import CarouselCard


class TestCarouselCard:
    def test_from_mapped_result_and_reference_formatting(self):
        """Test that the description is formatted as MM/DD, H:MMam/pm Location"""
        # Create a sample mapped result
        mapped_result = {
            "name": "Test Class",
            "day": "2025-04-16",
            "time": "5:00 PM",
            "location": "Chanhassen, Minnesota",
            "link": "lifetime-member://scheduledetail?classid=123",
            "imageUrl": "https://example.com/image.jpg",
        }

        # Create a carousel card from the mapped result
        card = CarouselCard.from_mapped_result_and_reference(mapped_result)

        # Check that the description is formatted correctly
        assert card.description == "04/16, 5:00pm Minnesota"

    def test_from_mapped_result_and_reference_with_book_prefix(self):
        """Test that the description is formatted correctly when the name has a Book prefix"""
        # Create a sample mapped result with Book prefix
        mapped_result = {
            "name": "Book Yoga Flow",
            "day": "2025-04-16",
            "time": "5:00 PM",
            "location": "Chanhassen, Minnesota",
            "link": "lifetime-member://scheduledetail?classid=123",
            "imageUrl": "https://example.com/image.jpg",
        }

        # Create a carousel card from the mapped result
        card = CarouselCard.from_mapped_result_and_reference(mapped_result)

        # Check that the description is formatted correctly
        assert card.description == "04/16, 5:00pm Minnesota"
        assert card.headlineText == "Book Yoga Flow"

    def test_from_mapped_result_and_reference_with_waitlist_prefix(self):
        """Test that the description is formatted correctly when the name has a Waitlist prefix"""
        # Create a sample mapped result with Waitlist prefix
        mapped_result = {
            "name": "Waitlist Yoga Flow",
            "day": "2025-04-16",
            "time": "5:00 PM",
            "location": "Chanhassen, Minnesota",
            "link": "lifetime-member://scheduledetail?classid=123",
            "imageUrl": "https://example.com/image.jpg",
        }

        # Create a carousel card from the mapped result
        card = CarouselCard.from_mapped_result_and_reference(mapped_result)

        # Check that the description is formatted correctly
        assert card.description == "04/16, 5:00pm Minnesota"
        assert card.headlineText == "Waitlist Yoga Flow"

    def test_from_mapped_result_and_reference_with_morning_time(self):
        """Test that the description is formatted correctly with morning time"""
        # Create a sample mapped result with morning time
        mapped_result = {
            "name": "Morning Yoga",
            "day": "2025-04-16",
            "time": "7:30 AM",
            "location": "Chanhassen, Minnesota",
            "link": "lifetime-member://scheduledetail?classid=123",
            "imageUrl": "https://example.com/image.jpg",
        }

        # Create a carousel card from the mapped result
        card = CarouselCard.from_mapped_result_and_reference(mapped_result)

        # Check that the description is formatted correctly
        assert card.description == "04/16, 7:30am Minnesota"

    def test_from_mapped_result_and_reference_with_noon_time(self):
        """Test that the description is formatted correctly with noon time"""
        # Create a sample mapped result with noon time
        mapped_result = {
            "name": "Noon Yoga",
            "day": "2025-04-16",
            "time": "12:00 PM",
            "location": "Chanhassen, Minnesota",
            "link": "lifetime-member://scheduledetail?classid=123",
            "imageUrl": "https://example.com/image.jpg",
        }

        # Create a carousel card from the mapped result
        card = CarouselCard.from_mapped_result_and_reference(mapped_result)

        # Check that the description is formatted correctly
        assert card.description == "04/16, 12:00pm Minnesota"
