import json
from unittest.mock import AsyncMock, patch

import pytest
from fastapi.testclient import TestClient
from pytest_httpx import HTTPXMock

from app.handlers.intents_handler import IntentResponse
from app.handlers.locations_handler import Club, ClubResponse
from app.main import app
from app.models.trainer_bio import (
    TrainerNameExtraction,
)

client = TestClient(app)


MOCK_SARAH_G_BIO_DICT = {
    "id": "bio123",
    "partyId": "party789",
    "employeeId": "empSarahG",
    "firstName": "Sarah",
    "lastName": "G.",
    "description": "An experienced and passionate fitness coach specializing in strength training and yoga.",
    "mediaId": "img456",
    "isConsentAccepted": True,
    "clubIds": ["club1", "club2"],
    "specialties": [
        {"id": "spec1", "title": "Strength Training"},
        {"id": "spec2", "title": "Yoga"},
    ],
    "certifications": [
        {"id": "cert1", "title": "Certified Personal Trainer"},
        {"id": "cert2", "title": "Yoga Alliance RYT 200"},
    ],
    "isPublished": True,
    "email": "<EMAIL>",
    "profilePath": "sarahg",
}

MOCK_CHRIS_P_BIO_DICT = {
    "id": "bio789",
    "partyId": "partyChrisP",
    "employeeId": "empChrisP",
    "firstName": "Chris",
    "lastName": "P.",
    "description": "Dedicated to helping clients achieve their mobility and endurance goals.",
    "mediaId": "img789",
    "isConsentAccepted": True,
    "clubIds": ["club3"],
    "specialties": [
        {"id": "spec3", "title": "Mobility"},
        {"id": "spec4", "title": "Endurance Running"},
    ],
    "certifications": [{"id": "cert3", "title": "Functional Movement Specialist"}],
    "isPublished": True,
    "email": "<EMAIL>",
    "profilePath": "chrisp",
}

MOCK_CHRIS_D_BIO_DICT = {
    "id": "bioChrisD",
    "partyId": "partyChrisD",
    "employeeId": "empChrisD",
    "firstName": "Chris",
    "lastName": "D.",
    "description": "Focuses on holistic wellness and nutritional guidance.",
    "isConsentAccepted": True,
    "clubIds": ["club1"],
    "specialties": [
        {"id": "spec5", "title": "Nutrition"},
        {"id": "spec6", "title": "Wellness Coaching"},
    ],
    "certifications": [],
    "isPublished": True,
    "profilePath": "chrisd",
}

DEFAULT_SCRATCH_PAD = {
    "name": "Test User",
    "conversationId": "integ-test-convo-123",
    "partyId": 12345,
    "memberId": 67890,
    "homeClub": "club1",
    "homeClubName": "Test Club",
    "todaysDate": "2024-07-30T10:00:00.000Z",
    "todaysWeekday": "Tuesday",
    "entityId": "test-entity-123",
    "appVersion": "10.0.0",
    "platform": "ios",
    "conversationSummary": "User asking about trainer bio",
    "rag": "trainer bio query",
    "locationNames": ["Test Club"],
    "nearbyEnabled": True,
}

MOCK_BIOS_API_BASE_URL = "https://test.scheduling.bios.api/scheduling/bios"
MOCK_BIOS_API_KEY = "test_bios_api_key"


@pytest.fixture(autouse=True)
def setup_env_vars(monkeypatch):
    """Set mock environment variables for BIOS API."""
    monkeypatch.setenv("SCHEDULING_BIOS_API_BASE_URL", MOCK_BIOS_API_BASE_URL)
    monkeypatch.setenv("SCHEDULING_BIOS_API_KEY", MOCK_BIOS_API_KEY)


@pytest.mark.asyncio
@pytest.mark.integration
async def test_bios_api_called_correctly_for_single_match(httpx_mock: HTTPXMock):
    question = "Tell me about Sarah G."
    extracted_trainer_name_from_mock_llm = "Sarah G."
    expected_bios_api_url = "https://api.lifetime.life/scheduling/bios/v2/bios/search"

    mock_intent_response = IntentResponse(intent="trainer_bio_query", confidence=0.99)

    mock_trainer_name_extraction = TrainerNameExtraction(
        trainer_name=extracted_trainer_name_from_mock_llm
    )

    mock_club_response = ClubResponse(clubs=[Club(clubId=1, name="Test Club")])

    mock_api_response_payload = {"results": [MOCK_SARAH_G_BIO_DICT], "count": 1}
    httpx_mock.add_response(
        url=expected_bios_api_url, method="POST", json=mock_api_response_payload, status_code=200
    )

    import re

    httpx_mock.add_response(
        url=re.compile(r"https://www\.lifetime\.life/bin/lt/nearestClubServlet\.locator\.json.*"),
        method="GET",
        json=[],
        status_code=200,
    )

    expected_formatted_bio_from_mock_llm = (
        f"Here's info on Sarah G.: {MOCK_SARAH_G_BIO_DICT['description']} "
        f"Specialties: Strength Training, Yoga. Certs: Certified Personal Trainer, Yoga Alliance RYT 200."
    )
    mock_llm_final_response_choice = AsyncMock()
    mock_llm_final_response_choice.message.content = expected_formatted_bio_from_mock_llm
    mock_llm_final_response = AsyncMock(choices=[mock_llm_final_response_choice])

    async def openai_side_effect(*args, **kwargs):
        response_format = kwargs.get("response_format")
        if response_format == IntentResponse:
            return mock_intent_response
        elif response_format == TrainerNameExtraction:
            return mock_trainer_name_extraction
        elif response_format == ClubResponse:
            return mock_club_response
        else:
            return mock_intent_response

    with (
        patch(
            "ai_agent_utils.services.openai.OpenAIClient.chat_structured",
            AsyncMock(side_effect=openai_side_effect),
        ) as mock_openai_structured,
        patch(
            "app.handlers.trainer_bio_handler.OpenAIClient.chat_create",
            AsyncMock(return_value=mock_llm_final_response),
        ) as mock_bio_format_openai,
    ):
        payload = {"question": question, "history": [], "scratch_pad": DEFAULT_SCRATCH_PAD}
        response = client.post("/v1/chats", json=payload)

    print(f"Response status: {response.status_code}")
    print(f"Response content: {response.content}")
    assert response.status_code == 200
    data = response.json()

    assert data["answer"] == expected_formatted_bio_from_mock_llm
    assert data["path"] == "trainer_bio_query_result"

    mock_openai_structured.assert_called()
    mock_bio_format_openai.assert_called_once()

    api_requests = httpx_mock.get_requests()
    assert len(api_requests) == 2

    bios_api_request = None
    for req in api_requests:
        if "scheduling/bios" in str(req.url):
            bios_api_request = req
            break

    assert bios_api_request is not None
    assert bios_api_request.method == "POST"
    assert str(bios_api_request.url) == expected_bios_api_url

    request_payload = json.loads(bios_api_request.content)
    print(f"Request payload: {request_payload}")
    assert request_payload["query"] == extracted_trainer_name_from_mock_llm

    bio_format_messages = mock_bio_format_openai.call_args[1]["messages"]
    system_prompt_for_formatting_llm = bio_format_messages[0]["content"]
    assert "Here's what I found for Sarah G.:" in system_prompt_for_formatting_llm
    assert MOCK_SARAH_G_BIO_DICT["description"] in system_prompt_for_formatting_llm
    assert MOCK_SARAH_G_BIO_DICT["specialties"][0]["title"] in system_prompt_for_formatting_llm


@pytest.mark.asyncio
@pytest.mark.integration
async def test_bios_api_handles_no_matches_response(httpx_mock: HTTPXMock):
    question = "Tell me about NonExistent Trainer."
    extracted_trainer_name_from_mock_llm = "NonExistent Trainer"
    expected_bios_api_url = "https://api.lifetime.life/scheduling/bios/v2/bios/search"

    mock_intent_response = IntentResponse(intent="trainer_bio_query", confidence=0.98)
    mock_trainer_name_extraction = TrainerNameExtraction(
        trainer_name=extracted_trainer_name_from_mock_llm
    )
    mock_club_response = ClubResponse(clubs=[Club(clubId=1, name="Test Club")])

    httpx_mock.add_response(
        url=expected_bios_api_url, method="POST", json={"results": [], "count": 0}, status_code=200
    )

    import re

    httpx_mock.add_response(
        url=re.compile(r"https://www\.lifetime\.life/bin/lt/nearestClubServlet\.locator\.json.*"),
        method="GET",
        json=[],
        status_code=200,
    )

    expected_formatted_no_match_msg = (
        f"Sorry, I couldn't find info for {extracted_trainer_name_from_mock_llm}."
    )
    mock_llm_final_response_choice = AsyncMock()
    mock_llm_final_response_choice.message.content = expected_formatted_no_match_msg
    mock_llm_final_response = AsyncMock(choices=[mock_llm_final_response_choice])

    async def openai_side_effect(*args, **kwargs):
        response_format = kwargs.get("response_format")
        if response_format == IntentResponse:
            return mock_intent_response
        elif response_format == TrainerNameExtraction:
            return mock_trainer_name_extraction
        elif response_format == ClubResponse:
            return mock_club_response
        else:
            return mock_intent_response

    with (
        patch(
            "ai_agent_utils.services.openai.OpenAIClient.chat_structured",
            AsyncMock(side_effect=openai_side_effect),
        ),
        patch(
            "app.handlers.trainer_bio_handler.OpenAIClient.chat_create",
            AsyncMock(return_value=mock_llm_final_response),
        ) as mock_bio_format_openai,
    ):
        payload = {"question": question, "history": [], "scratch_pad": DEFAULT_SCRATCH_PAD}
        response = client.post("/v1/chats", json=payload)

    assert response.status_code == 200
    data = response.json()
    assert data["answer"] == expected_formatted_no_match_msg

    api_requests = httpx_mock.get_requests()
    assert len(api_requests) == 2

    bios_api_request = None
    for req in api_requests:
        if "scheduling/bios" in str(req.url):
            bios_api_request = req
            break

    assert bios_api_request is not None
    assert json.loads(bios_api_request.content)["query"] == extracted_trainer_name_from_mock_llm

    bio_format_messages = mock_bio_format_openai.call_args[1]["messages"]
    system_prompt_for_formatting_llm = bio_format_messages[0]["content"]
    assert (
        f'I couldn\'t find any information for a trainer named "{extracted_trainer_name_from_mock_llm}"'
        in system_prompt_for_formatting_llm
    )


@pytest.mark.asyncio
@pytest.mark.integration
async def test_bios_api_handles_multiple_matches_response(httpx_mock: HTTPXMock):
    question = "Tell me about Chris."
    extracted_trainer_name_from_mock_llm = "Chris"
    expected_bios_api_url = "https://api.lifetime.life/scheduling/bios/v2/bios/search"

    mock_intent_response = IntentResponse(intent="trainer_bio_query", confidence=0.97)
    mock_trainer_name_extraction = TrainerNameExtraction(
        trainer_name=extracted_trainer_name_from_mock_llm
    )
    mock_club_response = ClubResponse(clubs=[Club(clubId=1, name="Test Club")])

    httpx_mock.add_response(
        url=expected_bios_api_url,
        method="POST",
        json={"results": [MOCK_CHRIS_P_BIO_DICT, MOCK_CHRIS_D_BIO_DICT], "count": 2},
        status_code=200,
    )

    import re

    httpx_mock.add_response(
        url=re.compile(r"https://www\.lifetime\.life/bin/lt/nearestClubServlet\.locator\.json.*"),
        method="GET",
        json=[],
        status_code=200,
    )

    expected_formatted_clarification_msg = "Found Chris P. and Chris D. Which one?"
    mock_llm_final_response_choice = AsyncMock()
    mock_llm_final_response_choice.message.content = expected_formatted_clarification_msg
    mock_llm_final_response = AsyncMock(choices=[mock_llm_final_response_choice])

    async def openai_side_effect(*args, **kwargs):
        response_format = kwargs.get("response_format")
        if response_format == IntentResponse:
            return mock_intent_response
        elif response_format == TrainerNameExtraction:
            return mock_trainer_name_extraction
        elif response_format == ClubResponse:
            return mock_club_response
        else:
            return mock_intent_response

    with (
        patch(
            "ai_agent_utils.services.openai.OpenAIClient.chat_structured",
            AsyncMock(side_effect=openai_side_effect),
        ),
        patch(
            "app.handlers.trainer_bio_handler.OpenAIClient.chat_create",
            AsyncMock(return_value=mock_llm_final_response),
        ) as mock_bio_format_openai,
    ):
        payload = {"question": question, "history": [], "scratch_pad": DEFAULT_SCRATCH_PAD}
        response = client.post("/v1/chats", json=payload)

    assert response.status_code == 200
    data = response.json()
    assert data["answer"] == expected_formatted_clarification_msg

    api_requests = httpx_mock.get_requests()
    assert len(api_requests) == 2

    bios_api_request = None
    for req in api_requests:
        if "scheduling/bios" in str(req.url):
            bios_api_request = req
            break

    assert bios_api_request is not None
    assert json.loads(bios_api_request.content)["query"] == extracted_trainer_name_from_mock_llm

    bio_format_messages = mock_bio_format_openai.call_args[1]["messages"]
    system_prompt_for_formatting_llm = bio_format_messages[0]["content"]
    assert "I found a few people matching that name:" in system_prompt_for_formatting_llm
    assert MOCK_CHRIS_P_BIO_DICT["firstName"] in system_prompt_for_formatting_llm
    assert MOCK_CHRIS_D_BIO_DICT["firstName"] in system_prompt_for_formatting_llm


@pytest.mark.asyncio
@pytest.mark.integration
async def test_bios_api_handles_http_error_response(httpx_mock: HTTPXMock):
    question = "Tell me about Sarah G."
    extracted_trainer_name_from_mock_llm = "Sarah G."
    expected_bios_api_url = "https://api.lifetime.life/scheduling/bios/v2/bios/search"

    mock_intent_response = IntentResponse(intent="trainer_bio_query", confidence=0.99)
    mock_trainer_name_extraction = TrainerNameExtraction(
        trainer_name=extracted_trainer_name_from_mock_llm
    )
    mock_club_response = ClubResponse(clubs=[Club(clubId=1, name="Test Club")])

    httpx_mock.add_response(
        url=expected_bios_api_url,
        method="POST",
        status_code=500,
        text="Internal Server Error from Mock Bios API",
    )

    import re

    httpx_mock.add_response(
        url=re.compile(r"https://www\.lifetime\.life/bin/lt/nearestClubServlet\.locator\.json.*"),
        method="GET",
        json=[],
        status_code=200,
    )

    expected_formatted_error_handling_msg = (
        f"Sorry, technical issue finding {extracted_trainer_name_from_mock_llm}."
    )
    mock_llm_final_response_choice = AsyncMock()
    mock_llm_final_response_choice.message.content = expected_formatted_error_handling_msg
    mock_llm_final_response = AsyncMock(choices=[mock_llm_final_response_choice])

    async def openai_side_effect(*args, **kwargs):
        response_format = kwargs.get("response_format")
        if response_format == IntentResponse:
            return mock_intent_response
        elif response_format == TrainerNameExtraction:
            return mock_trainer_name_extraction
        elif response_format == ClubResponse:
            return mock_club_response
        else:
            return mock_intent_response

    with (
        patch(
            "ai_agent_utils.services.openai.OpenAIClient.chat_structured",
            AsyncMock(side_effect=openai_side_effect),
        ),
        patch(
            "app.handlers.trainer_bio_handler.OpenAIClient.chat_create",
            AsyncMock(return_value=mock_llm_final_response),
        ) as mock_bio_format_openai,
    ):
        payload = {"question": question, "history": [], "scratch_pad": DEFAULT_SCRATCH_PAD}
        response = client.post("/v1/chats", json=payload)

    assert response.status_code == 200
    data = response.json()
    assert data["answer"] == expected_formatted_error_handling_msg

    api_requests = httpx_mock.get_requests()
    assert len(api_requests) == 2

    bio_format_messages = mock_bio_format_openai.call_args[1]["messages"]
    system_prompt_for_formatting_llm = bio_format_messages[0]["content"]
    assert (
        f'I couldn\'t find any information for a trainer named "{extracted_trainer_name_from_mock_llm}"'
        in system_prompt_for_formatting_llm
    )


@pytest.mark.asyncio
@pytest.mark.integration
async def test_no_bios_api_call_if_name_not_extracted(httpx_mock: HTTPXMock):
    """Ensures no call to scheduling-bios-api is made if trainer name is not extracted."""
    question = "Tell me about that person."

    mock_intent_response = IntentResponse(intent="trainer_bio_query", confidence=0.90)
    mock_trainer_name_extraction = TrainerNameExtraction(trainer_name=None)
    mock_club_response = ClubResponse(clubs=[Club(clubId=1, name="Test Club")])

    import re

    httpx_mock.add_response(
        url=re.compile(r"https://www\.lifetime\.life/bin/lt/nearestClubServlet\.locator\.json.*"),
        method="GET",
        json=[],
        status_code=200,
    )

    expected_direct_handler_response = "I couldn't quite catch the trainer's name you're asking about. Could you please tell me their name again? 😊"

    async def openai_side_effect(*args, **kwargs):
        response_format = kwargs.get("response_format")
        if response_format == IntentResponse:
            return mock_intent_response
        elif response_format == TrainerNameExtraction:
            return mock_trainer_name_extraction
        elif response_format == ClubResponse:
            return mock_club_response
        else:
            return mock_intent_response

    with (
        patch(
            "ai_agent_utils.services.openai.OpenAIClient.chat_structured",
            AsyncMock(side_effect=openai_side_effect),
        ) as mock_openai_structured,
        patch(
            "app.handlers.trainer_bio_handler.OpenAIClient.chat_create"
        ) as mock_bio_format_openai_should_not_be_called,
    ):
        payload = {"question": question, "history": [], "scratch_pad": DEFAULT_SCRATCH_PAD}
        response = client.post("/v1/chats", json=payload)

    assert response.status_code == 200
    data = response.json()
    assert data["answer"] == expected_direct_handler_response
    assert data["path"] == "trainer_bio_query_name_missing"

    mock_openai_structured.assert_called()
    mock_bio_format_openai_should_not_be_called.assert_not_called()

    api_requests = httpx_mock.get_requests()
    assert len(api_requests) == 1
    assert "nearestClubServlet" in str(api_requests[0].url)
